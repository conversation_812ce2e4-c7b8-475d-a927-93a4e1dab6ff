/* Theme Colors - Default Light Blue */
:root {
    /* Light Blue Theme (Default) */
    --primary-color: #3498db;       /* Main blue */
    --primary-dark: #2980b9;        /* Darker blue */
    --secondary-color: #2ecc71;     /* Green */
    --secondary-dark: #27ae60;      /* Darker green */
    --accent-color: #e74c3c;        /* Red accent */
    --accent-dark: #c0392b;         /* Darker red */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --card-bg: #ffffff;             /* Card background */
    --sidebar-bg: #f8f9fa;          /* Sidebar background */
    --sidebar-text: #343a40;        /* Sidebar text */
    --border-color: #dee2e6;        /* Border color */
    --success-color: #28a745;       /* Success green */
    --warning-color: #ffc107;       /* Warning yellow */
    --error-color: #dc3545;         /* Error red */
    --info-color: #17a2b8;          /* Info blue */
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --accent-color: #e74c3c;
    --accent-dark: #c0392b;
    --neutral-light: #343a40;
    --neutral-medium: #495057;
    --neutral-dark: #f8f9fa;
    --shadow: rgba(0, 0, 0, 0.3);
    --card-bg: #212529;
    --sidebar-bg: #212529;
    --sidebar-text: #f8f9fa;
    --border-color: #495057;
}

/* Purple Theme */
[data-theme="purple"] {
    --primary-color: #9b59b6;
    --primary-dark: #8e44ad;
    --secondary-color: #3498db;
    --secondary-dark: #2980b9;
    --accent-color: #e74c3c;
    --accent-dark: #c0392b;
    --neutral-light: #f8f9fa;
    --neutral-medium: #e9ecef;
    --neutral-dark: #343a40;
    --shadow: rgba(0, 0, 0, 0.1);
    --card-bg: #ffffff;
    --sidebar-bg: #f8f9fa;
    --sidebar-text: #343a40;
    --border-color: #dee2e6;
}

/* Green Theme */
[data-theme="green"] {
    --primary-color: #2ecc71;
    --primary-dark: #27ae60;
    --secondary-color: #3498db;
    --secondary-dark: #2980b9;
    --accent-color: #e74c3c;
    --accent-dark: #c0392b;
    --neutral-light: #f8f9fa;
    --neutral-medium: #e9ecef;
    --neutral-dark: #343a40;
    --shadow: rgba(0, 0, 0, 0.1);
    --card-bg: #ffffff;
    --sidebar-bg: #f8f9fa;
    --sidebar-text: #343a40;
    --border-color: #dee2e6;
}

/* Dark Blue Theme */
[data-theme="dark-blue"] {
    --primary-color: #34495e;
    --primary-dark: #2c3e50;
    --secondary-color: #3498db;
    --secondary-dark: #2980b9;
    --accent-color: #e74c3c;
    --accent-dark: #c0392b;
    --neutral-light: #f8f9fa;
    --neutral-medium: #e9ecef;
    --neutral-dark: #343a40;
    --shadow: rgba(0, 0, 0, 0.1);
    --card-bg: #ffffff;
    --sidebar-bg: #f8f9fa;
    --sidebar-text: #343a40;
    --border-color: #dee2e6;
}

/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--neutral-light);
    color: var(--neutral-dark);
    line-height: 1.6;
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
    touch-action: manipulation; /* Improve touch responsiveness */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .container {
        padding: 15px 10px;
    }

    /* App Header */
    .app-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .app-header h1 {
        font-size: 1.5rem;
    }

    /* Tabs */
    .tabs {
        flex-wrap: wrap;
    }

    .tab-button {
        padding: 8px 15px;
        font-size: 0.9rem;
        flex: 1;
        min-width: 100px;
        text-align: center;
    }

    /* Form Layout */
    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-group {
        min-width: 100%;
    }

    /* Invoice Items Table */
    #invoice-items-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    #invoice-items-table th,
    #invoice-items-table td {
        padding: 8px 5px;
    }

    /* Invoice Totals */
    .invoice-totals {
        width: 100%;
        margin-top: 20px;
    }

    /* Buttons */
    .btn {
        padding: 12px 15px;
        min-height: 44px; /* Better touch target */
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
    }

    /* Template Grid */
    .template-grid {
        grid-template-columns: 1fr;
    }

    /* Modal */
    .modal-content {
        width: 95%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
    }
}

/* App Header */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.app-header h1 {
    color: var(--primary-color);
}

.theme-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.theme-selector select {
    padding: 5px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--neutral-dark);
}

/* Tabs */
.tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: var(--neutral-dark);
    transition: all 0.2s ease;
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Tab Content */
.tab-content {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow);
    overflow: hidden;
}

.tab-pane {
    display: none;
    padding: 20px;
}

.tab-pane.active {
    display: block;
}

/* Form Styles */
.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.form-section h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group.full-width {
    flex-basis: 100%;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--card-bg);
    color: var(--neutral-dark);
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

/* Slider Styles */
.slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slider-container input[type="range"] {
    flex: 1;
    height: 8px;
    -webkit-appearance: none;
    appearance: none;
    background: var(--neutral-medium);
    border-radius: 4px;
    outline: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    transition: background 0.2s ease;
}

.slider-container input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    transition: background 0.2s ease;
    border: none;
}

.slider-container input[type="range"]:hover::-webkit-slider-thumb {
    background: var(--primary-dark);
}

.slider-container input[type="range"]:hover::-moz-range-thumb {
    background: var(--primary-dark);
}

.slider-value {
    min-width: 40px;
    text-align: center;
    font-weight: 500;
    color: var(--primary-color);
}

.reset-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--neutral-light);
    border: 1px solid var(--border-color);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 10px;
    padding: 0;
    font-size: 12px;
}

.reset-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: var(--neutral-dark);
    opacity: 0.7;
    font-size: 0.85em;
}

/* Invoice Items Table */
.invoice-items-container {
    margin-bottom: 20px;
}

#invoice-items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

#invoice-items-table th,
#invoice-items-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

#invoice-items-table th {
    background-color: var(--neutral-medium);
    font-weight: 500;
}

#invoice-items-table input,
#invoice-items-table textarea {
    width: 100%;
    padding: 5px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--card-bg);
    color: var(--neutral-dark);
}

#invoice-items-table textarea {
    resize: vertical;
    min-height: 60px;
}

/* Invoice Totals */
.invoice-totals {
    margin-left: auto;
    width: 300px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
}

.total-amount {
    font-weight: bold;
    font-size: 1.2em;
    color: var(--primary-color);
    border-top: 1px solid var(--border-color);
    padding-top: 10px;
    margin-top: 5px;
}

/* Buttons */
.btn {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
}

.btn-danger {
    background-color: var(--accent-color);
    color: white;
}

.btn-danger:hover {
    background-color: var(--accent-dark);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    background-color: var(--accent-color);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Preset Management Styles */
.preset-section {
    background-color: var(--neutral-light);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.preset-section h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.preset-section h3::before {
    content: "📋";
    font-size: 1.2em;
}

.preset-controls {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.preset-controls .btn {
    flex: 0 0 auto;
}

@media (max-width: 768px) {
    .preset-controls {
        flex-direction: column;
    }

    .preset-controls .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Template Selector */
.templates-container {
    padding: 20px;
}

.template-sections {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 10px;
}

.template-card {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px var(--shadow);
}

.template-card.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.template-preview {
    height: 200px;
    overflow: hidden;
    background-color: var(--neutral-medium);
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-info {
    padding: 15px;
    padding-bottom: 40px; /* Space for actions */
}

.template-info h4 {
    margin-bottom: 5px;
    color: var(--primary-color);
}

.template-info p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--neutral-dark);
}

.template-actions {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 10px;
    display: flex;
    gap: 5px;
}

.template-actions button {
    background: none;
    border: none;
    color: var(--neutral-dark);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.template-actions button:hover {
    color: var(--primary-color);
    background-color: rgba(0, 0, 0, 0.05);
}

.template-section-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 10px 0;
}

.custom-templates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.no-custom-templates {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    color: var(--neutral-medium);
    font-style: italic;
    background-color: var(--neutral-light);
    border-radius: 8px;
}

.custom-template-card .template-actions {
    display: flex;
    gap: 5px;
}

.btn-delete-template {
    color: var(--danger-color) !important;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
}

.modal-content {
    background-color: var(--card-bg);
    margin: 50px auto;
    width: 80%;
    max-width: 900px;
    border-radius: 8px;
    box-shadow: 0 5px 15px var(--shadow);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: var(--primary-color);
}

.close-modal {
    font-size: 24px;
    cursor: pointer;
    color: var(--neutral-dark);
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Invoice Preview */
#invoice-preview-container {
    background-color: white;
    padding: 20px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow);
}

/* Status Area */
#status-area {
    margin-top: 20px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

#status-area.success {
    display: block;
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

#status-area.error {
    display: block;
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--error-color);
    color: var(--error-color);
}

#status-area.info {
    display: block;
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid var(--info-color);
    color: var(--info-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .invoice-totals {
        width: 100%;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .template-grid {
        grid-template-columns: 1fr;
    }
}

/* Template Customizer Styles */
.customizer-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.customizer-header {
    margin-bottom: 20px;
}

.customizer-sections {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.customizer-section-nav {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
    border-right: 1px solid var(--border-color);
    padding-right: 15px;
}

.customizer-nav-btn {
    padding: 10px;
    text-align: left;
    background: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--neutral-dark);
}

.customizer-nav-btn:hover {
    background-color: var(--neutral-medium);
}

.customizer-nav-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.customizer-section-content {
    flex: 1;
    padding: 0 15px;
}

.customizer-section {
    display: none;
}

.customizer-section.active {
    display: block;
}

.customizer-section h4 {
    margin-bottom: 15px;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* Color Presets */
.color-presets {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.color-preset {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-preset:hover {
    transform: scale(1.1);
}

.color-preset.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* Preview Section */
.customizer-preview {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

#template-preview-container {
    background-color: white;
    min-height: 300px;
    border: 1px solid var(--border-color);
    margin: 10px auto;
    overflow: auto;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Page size and orientation styles */
#template-preview-container.orientation-portrait {
    aspect-ratio: 1 / 1.414; /* A4 ratio */
    max-width: 600px;
}

#template-preview-container.orientation-landscape {
    aspect-ratio: 1.414 / 1; /* A4 landscape ratio */
    max-width: 800px;
}

#template-preview-container.page-letter.orientation-portrait {
    aspect-ratio: 8.5 / 11;
}

#template-preview-container.page-letter.orientation-landscape {
    aspect-ratio: 11 / 8.5;
}

#template-preview-container.page-legal.orientation-portrait {
    aspect-ratio: 8.5 / 14;
}

#template-preview-container.page-legal.orientation-landscape {
    aspect-ratio: 14 / 8.5;
}

.customizer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .customizer-sections {
        flex-direction: column;
    }

    .customizer-section-nav {
        flex-direction: row;
        overflow-x: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        padding-right: 0;
        padding-bottom: 15px;
    }

    .customizer-nav-btn {
        white-space: nowrap;
    }
}

/* Notification Popup */
#status-area {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-weight: 500;
    z-index: 9999;
    max-width: 350px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    display: none;
}

#status-area.show {
    opacity: 1;
    transform: translateY(0);
}

#status-area.success {
    background-color: var(--success-color);
    color: white;
}

#status-area.error {
    background-color: var(--error-color);
    color: white;
}

#status-area.info {
    background-color: var(--info-color);
    color: white;
}

#status-area.warning {
    background-color: var(--warning-color);
    color: #333;
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }

    #invoice-preview-container,
    #invoice-preview-container * {
        visibility: visible;
    }

    #invoice-preview-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        box-shadow: none;
        border: none;
    }
}
