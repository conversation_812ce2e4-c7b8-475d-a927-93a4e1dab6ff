<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Maker</title>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- SheetJS for Excel generation -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="app-header">
            <h1>Invoice Maker</h1>
        </div>

        <div class="tabs">
            <button class="tab-button active" data-tab="invoice-editor">Invoice Editor</button>
            <button class="tab-button" data-tab="template-selector">Templates</button>
            <button class="tab-button" data-tab="template-customizer">Customize Template</button>
            <button class="tab-button" data-tab="settings">Settings</button>
        </div>

        <div class="tab-content">
            <!-- Invoice Editor Tab -->
            <div class="tab-pane active" id="invoice-editor">
                <div class="invoice-form">
                    <!-- Preset Management Section -->
                    <div class="form-section">
                        <h3>Invoice Presets</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="preset-selector">Load Preset</label>
                                <select id="preset-selector">
                                    <option value="">Select a preset...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="preset-name">Preset Name</label>
                                <input type="text" id="preset-name" placeholder="Enter preset name">
                            </div>
                        </div>
                        <div class="form-row">
                            <button id="save-preset-btn" class="btn btn-secondary">
                                <i class="fas fa-save"></i> Save Preset
                            </button>
                            <button id="delete-preset-btn" class="btn btn-danger" disabled>
                                <i class="fas fa-trash"></i> Delete Preset
                            </button>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Invoice Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="invoice-number">Invoice Number</label>
                                <input type="text" id="invoice-number" placeholder="INV-001">
                            </div>
                            <div class="form-group">
                                <label for="invoice-date">Invoice Date</label>
                                <input type="date" id="invoice-date">
                            </div>
                            <div class="form-group">
                                <label for="due-date">Due Date</label>
                                <input type="date" id="due-date">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Your Business Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="business-name">Business Name</label>
                                <input type="text" id="business-name" placeholder="Your Business Name">
                            </div>
                            <div class="form-group">
                                <label for="business-address">Address</label>
                                <textarea id="business-address" placeholder="Your Business Address"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="business-phone">Phone</label>
                                <input type="tel" id="business-phone" placeholder="Phone Number">
                            </div>
                            <div class="form-group">
                                <label for="business-email">Email</label>
                                <input type="email" id="business-email" placeholder="Email Address">
                            </div>
                            <div class="form-group">
                                <label for="business-website">Website</label>
                                <input type="url" id="business-website" placeholder="Website URL">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Client Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="client-name">Client Name</label>
                                <input type="text" id="client-name" placeholder="Client Name">
                            </div>
                            <div class="form-group">
                                <label for="client-address">Address</label>
                                <textarea id="client-address" placeholder="Client Address"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="client-phone">Phone</label>
                                <input type="tel" id="client-phone" placeholder="Phone Number">
                            </div>
                            <div class="form-group">
                                <label for="client-email">Email</label>
                                <input type="email" id="client-email" placeholder="Email Address">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Invoice Items</h3>
                        <div class="invoice-items-container">
                            <table id="invoice-items-table">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>Quantity</th>
                                        <th>Unit Price</th>
                                        <th>Tax (%)</th>
                                        <th>Amount</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="invoice-items-body">
                                    <!-- Items will be added here dynamically -->
                                </tbody>
                            </table>
                            <button id="add-item-btn" class="btn btn-secondary">
                                <i class="fas fa-plus"></i> Add Item
                            </button>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Invoice Totals</h3>
                        <div class="invoice-totals">
                            <div class="total-row">
                                <span>Subtotal:</span>
                                <span id="subtotal">$0.00</span>
                            </div>
                            <div class="total-row">
                                <span>Tax:</span>
                                <span id="tax-total">$0.00</span>
                            </div>
                            <div class="total-row total-amount">
                                <span>Total:</span>
                                <span id="grand-total">$0.00</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="notes-title">Notes Section Title</label>
                                <input type="text" id="notes-title" value="Notes" placeholder="Section title for notes">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="invoice-notes">Notes Content</label>
                                <textarea id="invoice-notes" rows="5" placeholder="Payment terms, thank you message, etc."></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button id="preview-btn" class="btn btn-secondary">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                        <button id="export-pdf-btn" class="btn btn-primary">
                            <i class="fas fa-file-pdf"></i> Export as PDF
                        </button>
                        <button id="export-excel-btn" class="btn btn-primary">
                            <i class="fas fa-file-excel"></i> Export as Excel
                        </button>
                    </div>
                </div>
            </div>

            <!-- Template Selector Tab -->
            <div class="tab-pane" id="template-selector">
                <div class="templates-container">
                    <h3>Choose an Invoice Template</h3>
                    <p>Select a template to use for your invoice exports.</p>

                    <div class="template-sections">
                        <h4>Built-in Templates</h4>
                        <div class="template-grid" id="builtin-templates">
                            <div class="template-card active" data-template="classic" data-template-type="builtin">
                                <div class="template-preview">
                                    <img src="templates/classic-preview.png" alt="Classic Template">
                                </div>
                                <div class="template-info">
                                    <h4>Classic</h4>
                                    <p>A clean, professional invoice template.</p>
                                </div>
                                <div class="template-actions">
                                    <button class="btn-edit-template" title="Edit Template">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="template-card" data-template="modern" data-template-type="builtin">
                                <div class="template-preview">
                                    <img src="templates/modern-preview.png" alt="Modern Template">
                                </div>
                                <div class="template-info">
                                    <h4>Modern</h4>
                                    <p>A sleek, contemporary design with accent colors.</p>
                                </div>
                                <div class="template-actions">
                                    <button class="btn-edit-template" title="Edit Template">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="template-card" data-template="minimal" data-template-type="builtin">
                                <div class="template-preview">
                                    <img src="templates/minimal-preview.png" alt="Minimal Template">
                                </div>
                                <div class="template-info">
                                    <h4>Minimal</h4>
                                    <p>A simple, minimalist design focused on clarity.</p>
                                </div>
                                <div class="template-actions">
                                    <button class="btn-edit-template" title="Edit Template">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="template-card" data-template="creative" data-template-type="builtin">
                                <div class="template-preview">
                                    <img src="templates/creative-preview.png" alt="Creative Template">
                                </div>
                                <div class="template-info">
                                    <h4>Creative</h4>
                                    <p>A bold, colorful design for creative businesses.</p>
                                </div>
                                <div class="template-actions">
                                    <button class="btn-edit-template" title="Edit Template">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="template-section-divider"></div>

                        <div class="custom-templates-header">
                            <h4>Your Custom Templates</h4>
                            <button id="create-template-btn" class="btn btn-secondary">
                                <i class="fas fa-plus"></i> Create New Template
                            </button>
                        </div>

                        <div class="template-grid" id="custom-templates">
                            <!-- Custom templates will be loaded here dynamically -->
                            <div class="no-custom-templates">You haven't created any custom templates yet.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Template Customizer Tab -->
            <div class="tab-pane" id="template-customizer">
                <div class="customizer-container">
                    <div class="customizer-header">
                        <h3>Customize Your Invoice Template</h3>
                        <p>Personalize your invoice appearance with these customization options.</p>
                    </div>

                    <div class="customizer-sections">
                        <div class="customizer-section-nav">
                            <button class="customizer-nav-btn active" data-section="general">General</button>
                            <button class="customizer-nav-btn" data-section="header">Header</button>
                            <button class="customizer-nav-btn" data-section="table">Items Table</button>
                            <button class="customizer-nav-btn" data-section="footer">Footer</button>
                            <button class="customizer-nav-btn" data-section="fonts">Fonts</button>
                            <button class="customizer-nav-btn" data-section="colors">Colors</button>
                        </div>

                        <div class="customizer-section-content">
                            <!-- General Section -->
                            <div class="customizer-section active" id="general-section">
                                <h4>General Layout</h4>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="template-name">Template Name</label>
                                        <input type="text" id="template-name" placeholder="My Custom Template">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="page-size">Page Size</label>
                                        <select id="page-size">
                                            <option value="a4">A4</option>
                                            <option value="letter">Letter</option>
                                            <option value="legal">Legal</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="page-orientation">Orientation</label>
                                        <select id="page-orientation">
                                            <option value="portrait">Portrait</option>
                                            <option value="landscape">Landscape</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="margin-top">Top Margin (mm)</label>
                                        <input type="number" id="margin-top" value="20" min="0" max="50">
                                    </div>

                                    <div class="form-group">
                                        <label for="margin-right">Right Margin (mm)</label>
                                        <input type="number" id="margin-right" value="20" min="0" max="50">
                                    </div>

                                    <div class="form-group">
                                        <label for="margin-bottom">Bottom Margin (mm)</label>
                                        <input type="number" id="margin-bottom" value="20" min="0" max="50">
                                    </div>

                                    <div class="form-group">
                                        <label for="margin-left">Left Margin (mm)</label>
                                        <input type="number" id="margin-left" value="20" min="0" max="50">
                                    </div>
                                </div>
                            </div>

                            <!-- Header Section -->
                            <div class="customizer-section" id="header-section">
                                <h4>Invoice Header</h4>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="header-height">Header Height (mm)</label>
                                        <input type="number" id="header-height" value="50" min="20" max="100">
                                    </div>

                                    <div class="form-group">
                                        <label for="header-bg-color">Background Color</label>
                                        <input type="color" id="header-bg-color" value="#ffffff">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="invoice-title">Invoice Title</label>
                                        <input type="text" id="invoice-title" value="INVOICE">
                                    </div>

                                    <div class="form-group">
                                        <label for="invoice-title-color">Title Color</label>
                                        <input type="color" id="invoice-title-color" value="#3498db">
                                    </div>

                                    <div class="form-group">
                                        <label for="invoice-title-size">Title Size (pt)</label>
                                        <input type="number" id="invoice-title-size" value="24" min="12" max="48">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="company-info-align">Company Info Alignment</label>
                                        <select id="company-info-align">
                                            <option value="left">Left</option>
                                            <option value="right" selected>Right</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="company-name-size">Company Name Size (pt)</label>
                                        <input type="number" id="company-name-size" value="14" min="10" max="24">
                                    </div>
                                </div>
                            </div>

                            <!-- Table Section -->
                            <div class="customizer-section" id="table-section">
                                <h4>Items Table</h4>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="table-style">Table Style</label>
                                        <select id="table-style">
                                            <option value="classic">Classic (Grid Lines)</option>
                                            <option value="modern">Modern (Header + Row Lines)</option>
                                            <option value="minimal">Minimal (Row Lines Only)</option>
                                            <option value="none">No Borders</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="table-header-bg">Header Background</label>
                                        <input type="color" id="table-header-bg" value="#f8f9fa">
                                    </div>

                                    <div class="form-group">
                                        <label for="table-header-text">Header Text Color</label>
                                        <input type="color" id="table-header-text" value="#000000">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="table-row-alt">Alternating Row Colors</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="table-row-alt" checked>
                                            <label for="table-row-alt"></label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="table-row-alt-color">Alternate Row Color</label>
                                        <input type="color" id="table-row-alt-color" value="#f2f2f2">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="table-border-color">Border Color</label>
                                        <input type="color" id="table-border-color" value="#dee2e6">
                                    </div>

                                    <div class="form-group">
                                        <label for="table-border-width">Border Width (px)</label>
                                        <input type="number" id="table-border-width" value="1" min="0" max="5" step="0.5">
                                    </div>
                                </div>

                                <h4>Column Visibility</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="col-item">Item Description</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="col-item" checked>
                                            <label for="col-item"></label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="col-quantity">Quantity</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="col-quantity" checked>
                                            <label for="col-quantity"></label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="col-price">Unit Price</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="col-price" checked>
                                            <label for="col-price"></label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="col-tax">Tax</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="col-tax" checked>
                                            <label for="col-tax"></label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="col-amount">Amount</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="col-amount" checked>
                                            <label for="col-amount"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer Section -->
                            <div class="customizer-section" id="footer-section">
                                <h4>Invoice Footer</h4>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="footer-text">Footer Text</label>
                                        <input type="text" id="footer-text" value="Thank you for your business!">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="footer-text-color">Text Color</label>
                                        <input type="color" id="footer-text-color" value="#6c757d">
                                    </div>

                                    <div class="form-group">
                                        <label for="footer-font-size">Font Size (pt)</label>
                                        <input type="number" id="footer-font-size" value="10" min="8" max="14">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="show-page-numbers">Show Page Numbers</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="show-page-numbers" checked>
                                            <label for="show-page-numbers"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Fonts Section -->
                            <div class="customizer-section" id="fonts-section">
                                <h4>Font Settings</h4>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="primary-font">Primary Font</label>
                                        <select id="primary-font">
                                            <option value="helvetica">Helvetica</option>
                                            <option value="times">Times</option>
                                            <option value="courier">Courier</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="text-size-normal">Normal Text Size (pt)</label>
                                        <input type="number" id="text-size-normal" value="10" min="8" max="14">
                                    </div>

                                    <div class="form-group">
                                        <label for="text-size-small">Small Text Size (pt)</label>
                                        <input type="number" id="text-size-small" value="8" min="6" max="12">
                                    </div>
                                </div>
                            </div>

                            <!-- Colors Section -->
                            <div class="customizer-section" id="colors-section">
                                <h4>Color Scheme</h4>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="primary-color">Primary Color</label>
                                        <input type="color" id="primary-color" value="#3498db">
                                    </div>

                                    <div class="form-group">
                                        <label for="secondary-color">Secondary Color</label>
                                        <input type="color" id="secondary-color" value="#2ecc71">
                                    </div>

                                    <div class="form-group">
                                        <label for="accent-color">Accent Color</label>
                                        <input type="color" id="accent-color" value="#e74c3c">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="text-color">Text Color</label>
                                        <input type="color" id="text-color" value="#333333">
                                    </div>

                                    <div class="form-group">
                                        <label for="background-color">Background Color</label>
                                        <input type="color" id="background-color" value="#ffffff">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Color Presets</label>
                                        <div class="color-presets">
                                            <button class="color-preset" data-preset="blue" style="background-color: #3498db;"></button>
                                            <button class="color-preset" data-preset="green" style="background-color: #2ecc71;"></button>
                                            <button class="color-preset" data-preset="purple" style="background-color: #9b59b6;"></button>
                                            <button class="color-preset" data-preset="red" style="background-color: #e74c3c;"></button>
                                            <button class="color-preset" data-preset="orange" style="background-color: #f39c12;"></button>
                                            <button class="color-preset" data-preset="teal" style="background-color: #1abc9c;"></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="customizer-preview">
                        <h4>Preview</h4>
                        <div id="template-preview-container">
                            <!-- Preview will be generated here -->
                        </div>
                    </div>

                    <div class="customizer-actions">
                        <button id="save-template-btn" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Template
                        </button>
                        <button id="reset-template-btn" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> Reset Changes
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane" id="settings">
                <div class="settings-container">
                    <h3>Invoice Settings</h3>

                    <div class="form-section">
                        <h4>Default Values</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="default-currency">Currency</label>
                                <select id="default-currency">
                                    <option value="USD">USD ($) - US Dollar</option>
                                    <option value="EUR">EUR (€) - Euro</option>
                                    <option value="GBP">GBP (£) - British Pound</option>
                                    <option value="AUD">AUD ($) - Australian Dollar</option>
                                    <option value="CAD">CAD ($) - Canadian Dollar</option>
                                    <option value="JPY">JPY (¥) - Japanese Yen</option>
                                    <option value="CNY">CNY (¥) - Chinese Yuan</option>
                                    <option value="INR">INR (₹) - Indian Rupee</option>
                                    <option value="BRL">BRL (R$) - Brazilian Real</option>
                                    <option value="CHF">CHF (Fr) - Swiss Franc</option>
                                    <option value="NZD">NZD ($) - New Zealand Dollar</option>
                                    <option value="ZAR">ZAR (R) - South African Rand</option>
                                    <option value="HKD">HKD ($) - Hong Kong Dollar</option>
                                    <option value="SGD">SGD ($) - Singapore Dollar</option>
                                    <option value="MXN">MXN ($) - Mexican Peso</option>
                                    <option value="PHP">PHP (₱) - Philippine Peso</option>
                                    <option value="SEK">SEK (kr) - Swedish Krona</option>
                                    <option value="NOK">NOK (kr) - Norwegian Krone</option>
                                    <option value="DKK">DKK (kr) - Danish Krone</option>
                                    <option value="PLN">PLN (zł) - Polish Złoty</option>
                                    <option value="RUB">RUB (₽) - Russian Ruble</option>
                                    <option value="TRY">TRY (₺) - Turkish Lira</option>
                                    <option value="AED">AED (د.إ) - UAE Dirham</option>
                                    <option value="SAR">SAR (﷼) - Saudi Riyal</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="default-tax-rate">Default Tax Rate (%)</label>
                                <input type="number" id="default-tax-rate" min="0" max="100" step="0.1" value="10">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4>PDF Export Settings</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pdf-page-size">Page Size</label>
                                <select id="pdf-page-size">
                                    <option value="a4">A4</option>
                                    <option value="letter">Letter</option>
                                    <option value="legal">Legal</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="pdf-orientation">Orientation</label>
                                <select id="pdf-orientation">
                                    <option value="portrait">Portrait</option>
                                    <option value="landscape">Landscape</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="summary-spacing">Summary Spacing (mm)</label>
                                <div class="slider-container">
                                    <input type="range" id="summary-spacing" min="20" max="100" value="50" step="5">
                                    <span class="slider-value">50</span>
                                    <button id="reset-spacing-btn" class="reset-btn" title="Reset to default (50mm)">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </div>
                                <small>Controls the horizontal spacing between labels (Subtotal, Tax, Total) and their amounts</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="summary-alignment">Amount Alignment</label>
                                <select id="summary-alignment">
                                    <option value="left">Left Aligned</option>
                                    <option value="right" selected>Right Aligned</option>
                                </select>
                                <small>Controls how amount values are aligned in the summary section</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button id="save-settings-btn" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                        <button id="reset-settings-btn" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> Reset to Defaults
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Modal -->
        <div id="preview-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Invoice Preview</h2>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="invoice-preview-container">
                        <!-- Preview content will be generated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="modal-export-pdf-btn" class="btn btn-primary">
                        <i class="fas fa-file-pdf"></i> Export as PDF
                    </button>
                    <button id="modal-export-excel-btn" class="btn btn-primary">
                        <i class="fas fa-file-excel"></i> Export as Excel
                    </button>
                    <button id="modal-close-btn" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
            </div>
        </div>

        <!-- Status Area -->
        <div id="status-area"></div>
    </div>

    <!-- Templates directory for storing template images and configurations -->
    <script src="templates.js"></script>
    <script src="script.js"></script>
</body>
</html>
